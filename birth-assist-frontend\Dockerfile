FROM node:22.16.0-alpine AS builder
WORKDIR /frontend-app
COPY package*.json /frontend-app
RUN npm install
COPY . /frontend-app
RUN npm run build --omit=dev

FROM node:22.16.0-alpine AS runner
WORKDIR /frontend-app
COPY --from=builder /frontend-app/dist /frontend-app/dist
COPY --from=builder /frontend-app/node_modules /frontend-app/node_modules
COPY --from=builder /frontend-app/package*.json /frontend-app
CMD ["npm", "run", "serve:ssr:birth-assist-frontend"]