limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=general_limit:10m rate=30r/s;

upstream frontend {
    server birth-assist-frontend:4000;
}

upstream api {
    server birth-assist-backend:3000;
}

server {
    listen 443 ssl;
    http2 on;
    server_name dulatkeresek.hu www.dulatkeresek.hu;
    
    ssl_certificate /etc/letsencrypt/live/dulatkeresek.hu/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/dulatkeresek.hu/privkey.pem;
    
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    location /api {
        limit_req zone=api_limit burst=20 nodelay;
        
        allow **********/12;
        deny all;
        
        valid_referers server_names;
        if ($invalid_referer) {
            return 403;
        }
        
        proxy_pass http://api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        add_header Access-Control-Allow-Origin "https://dulatkeresek.hu";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";
        add_header Access-Control-Allow-Credentials true;
    }

    location / {
        limit_req zone=general_limit burst=50 nodelay;
        
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
        proxy_read_timeout 60s;
    }

    location ~* \.(php|asp|aspx|jsp)$ {
        return 444;
    }
    
    location ~* \.(git|env|log|sql)$ {
        deny all;
    }
}

server {
    listen 80;
    server_name dulatkeresek.hu www.dulatkeresek.hu;
    return 301 https://$host$request_uri;
}