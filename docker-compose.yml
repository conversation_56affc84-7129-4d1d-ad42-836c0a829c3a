services:
  backend:
    image: birth-assist-backend
    pull_policy: never
    container_name: birth-assist-backend
    build:
      context: ./birth-assist-backend
      dockerfile: Dockerfile
    env_file:
      - ./birth-assist-backend/.env
    networks:
      - internal-network

  frontend:
    image: birth-assist-frontend
    pull_policy: never
    container_name: birth-assist-frontend
    build:
      context: ./birth-assist-frontend
      dockerfile: Dockerfile
      target: runner
    env_file:
      - ./birth-assist-frontend/.env
    networks:
      - internal-network
    depends_on:
      - backend

  nginx:
    image: birth-assist-proxy
    pull_policy: never
    container_name: birth-assist-proxy
    build:
      context: ./birth-assist-proxy
      dockerfile: Dockerfile
    ports:
      - "80:80"
      - "443:443"
    networks:
      - internal-network
    depends_on:
      - frontend
      - backend
    volumes:
      - /etc/letsencrypt:/etc/letsencrypt:ro

networks:
  internal-network:
    driver: bridge
    internal: false